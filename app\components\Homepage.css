/* Import Google Fonts */
@import url("https://fonts.googleapis.com/css2?family=Merriweather:wght@400;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Josefin+Sans:ital,wght@0,400;1,400&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Battambang:wght@400;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Barlow+Condensed:wght@300;400;500;600;700&display=swap");

.homepage {
  position: relative;
  background-color: #fbfbf9;

  width: 100%;
  min-height: 100vh;
  /* Add enough height to accommodate all absolutely positioned content */
  height: auto;
  padding-bottom: 0;

  margin: 0 auto;
}

.homepage__hero-title {
  position: absolute;
  flex-shrink: 0;
  font-family: "Baskerville Old Face", "Times New Roman", serif;
  font-size: 96px;
  color: #966f33;
  height: 327px;
  line-height: 115.83px;
  left: 67px;
  top: 133px;
  width: 892px;
}

.homepage__features-section {
  display: flex;
  position: absolute;
  left: 56px;
  flex-direction: column;
  gap: 40px;
  align-items: flex-start;
  height: 277px;
  top: 509px;
  width: 310px;
}

.homepage__feature-item {
  display: flex;
  position: relative;
  gap: 20px;
  align-items: center;
}

.homepage__feature-icon {
  position: relative;
  height: 75px;
  width: 59px;
}

.homepage__feature-icon--medium {
  height: 63px;
  width: 59px;
}

.homepage__feature-icon--small {
  height: 59px;
  width: 59px;
}

.homepage__feature-content {
  display: flex;
  position: relative;
  flex-direction: column;
  gap: 10px;
  align-items: flex-start;
  width: 173px;
}

.homepage__feature-content--wide {
  width: 233px;
}

.homepage__feature-title {
  position: relative;
  align-self: stretch;
  font-family: "Battambang", sans-serif;
  font-size: 24px;
  letter-spacing: -0.05em;
  line-height: 24px;
  color: #966f33;
}

.homepage__feature-description {
  position: relative;
  align-self: stretch;
  font-family: "Source Sans Pro", sans-serif;
  font-size: 18px;
  letter-spacing: -0.05em;
  line-height: 20px;
  color: black;
}

.homepage__main-section {
  display: flex;
  position: absolute;
  flex-direction: column;
  gap: 80px;
  align-items: flex-start;
  height: 441px;
  left: 1087px;
  top: 346px;
  width: 950px;
}

.homepage__main-title {
  position: relative;
  align-self: stretch;
  font-family: "Baskerville Old Face", "Times New Roman", serif;
  font-size: 72px;
  letter-spacing: -0.05em;
  color: black;
  height: 155px;
  line-height: 65.28px;
}

.homepage__main-content {
  display: flex;
  position: relative;
  flex-direction: column;
  gap: 0;
  align-items: flex-start;
  width: 797px;
}

.homepage__main-quote {
  position: relative;
  align-self: stretch;
  font-family: "Josefin Sans", sans-serif;
  font-size: 32px;
  font-style: italic;
  letter-spacing: -0.05em;
  line-height: 32px;
  color: black;
  height: 209px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .homepage {
    width: 100%;
    height: auto;
    min-height: 100vh;
  }

  .homepage__main-wrapper {
    min-height: 1000px;
  }

  .homepage__hero-title {
    height: auto;
    font-size: 72px;
    left: 5%;
    top: 150px;
    width: 90%;
  }

  .homepage__features-section {
    height: auto;
    left: 5%;
    top: 400px;
    width: 90%;
  }

  .homepage__main-section {
    height: auto;
    left: 5%;
    top: 700px;
    width: 90%;
  }

  .homepage__main-title {
    height: auto;
    font-size: 48px;
  }

  .homepage__main-content {
    gap: 20px;
    width: 100%;
  }

  .homepage__main-quote {
    height: auto;
    font-size: 24px;
  }
}

@media (max-width: 640px) {
  .homepage__main-wrapper {
    min-height: 800px;
  }

  .homepage__hero-title {
    font-size: 48px;
    top: 120px;
  }

  .homepage__features-section {
    top: 300px;
  }

  .homepage__main-section {
    top: 600px;
  }

  .homepage__main-title {
    font-size: 36px;
  }

  .homepage__main-quote {
    font-size: 18px;
  }

  .homepage__feature-item {
    flex-direction: column;
    text-align: center;
  }
}

/* Main content wrapper to contain absolutely positioned elements */
.homepage__main-wrapper {
  position: relative;
  width: 100%;
  /* Set minimum height to accommodate all absolutely positioned content */
  min-height: 900px;
  height: auto;
}

/* Content Creation & Research Engine Section */
.homepage__content-engine-section {
  overflow: hidden;
  position: relative;
  margin: 50px auto 0;
  max-width: 1920px;
  background-color: #fbf8f1;
  min-height: 871px;
  width: 100%;
  display: flex;
  align-items: center;
}

.homepage__content-engine-main-image {
  object-fit: cover;
  position: absolute;
  left: 0;
  top: 0;
  flex-shrink: 0;
  height: 100%;
  width: 48.6%;
  max-width: 933px;
}

.homepage__content-engine-ratings {
  display: inline-flex;
  position: absolute;
  gap: 6px;
  align-items: center;
  height: 37px;
  right: 64px;
  bottom: 94px;
  width: 124px;
}

.homepage__content-engine-rating-star {
  object-fit: cover;
  position: relative;
  aspect-ratio: 38/37;
  height: 37px;
  width: 38px;
}

.homepage__content-engine-content {
  display: flex;
  position: absolute;
  flex-direction: column;
  gap: 56px;
  align-items: flex-start;
  right: 434px;
  top: 50%;
  transform: translateY(-50%);
  width: 498px;
  z-index: 2;
}

.homepage__content-engine-title {
  position: relative;
  align-self: stretch;
  font-family: "Baskerville Old Face", "Times New Roman", serif;
  font-size: 72px;
  color: black;
  line-height: 78.39px;
}

.homepage__content-engine-title-text {
  font-family: "Baskerville Old Face", "Times New Roman", serif;
  font-size: 72px;
  color: black;
}

.homepage__content-engine-description {
  position: relative;
  font-size: 32px;
  font-weight: 300;
  line-height: 36px;
  color: black;
  width: 480px;
}

.homepage__content-engine-description-text {
  font-size: 32px;
  color: black;
}

/* Responsive styles for Content Creation & Research Engine Section */
@media (max-width: 768px) {
  .homepage__content-engine-section {
    flex-direction: column;
    padding: 40px 20px;
    min-height: 600px;
    margin-top: 50px;
  }

  .homepage__content-engine-main-image {
    position: relative;
    width: 60%;
    height: auto;
    max-height: 500px;
    margin: 0 auto 30px;
  }

  .homepage__content-engine-ratings {
    position: relative;
    right: auto;
    bottom: auto;
    justify-content: center;
    margin: 20px auto;
  }

  .homepage__content-engine-content {
    position: relative;
    right: auto;
    top: auto;
    transform: none;
    gap: 32px;
    margin: 0 auto;
    width: 100%;
    max-width: 600px;
    text-align: center;
  }

  .homepage__content-engine-title {
    font-size: 48px;
  }

  .homepage__content-engine-title-text {
    font-size: 48px;
  }

  .homepage__content-engine-description {
    width: 100%;
    font-size: 24px;
  }

  .homepage__content-engine-description-text {
    font-size: 24px;
  }
}

/* Why Us Section */
.homepage__why-us-section {
  box-sizing: border-box;
  position: relative;
  padding: 80px 112px 0;
  margin: 0 auto;
  width: 100%;
  max-width: none;
  background-color: #fbfbf9;
  min-height: 871px;
}

.homepage__why-us-title {
  position: absolute;
  font-family: "Baskerville Old Face", "Times New Roman", serif;
  font-size: 128px;
  color: #966f33;
  height: 117px;
  line-height: 117px;
  left: 110px;
  top: 78px;
  width: 377px;
}

.homepage__why-us-content {
  display: inline-flex;
  position: absolute;
  top: 0;
  gap: 64px;
  align-items: center;
  height: 889px;
  left: 276px;
  width: 1469px;
}

.homepage__why-us-image {
  object-fit: cover;
  position: relative;
  aspect-ratio: 764/889;
  height: 889px;
  width: 764px;
}

.homepage__why-us-features {
  display: flex;
  position: relative;
  flex-direction: column;
  gap: 20px;
  align-items: flex-start;
  width: 643px;
}

.homepage__feature-item-container {
  position: relative;
  width: 100%;
}

.homepage__feature-item-header {
  display: flex;
  position: relative;
  gap: 14px;
  align-items: flex-start;
  height: 42px;
}

.homepage__feature-item-icon {
  position: relative;
  width: 42px;
  height: 42px;
  flex-shrink: 0;
}

.homepage__feature-item-title {
  font-family: "Baskerville Old Face", "Times New Roman", serif;
  font-size: 36px;
  letter-spacing: -0.025em;
  line-height: 36px;
  color: #966f33;
  height: 35px;
}

.homepage__feature-item-description {
  margin-top: 12px;
  margin-left: 56px;
  font-family: "Source Sans Pro", sans-serif;
  font-size: 20px;
  line-height: 24px;
  color: black;
  width: 481px;
}

/* Responsive styles for Why Us Section */
@media (max-width: 768px) {
  .homepage__why-us-section {
    padding: 40px 20px 0;
    max-width: 991px;
  }

  .homepage__why-us-title {
    left: 20px;
    top: 40px;
    font-size: 96px;
    height: 70px;
    width: 300px;
  }

  .homepage__why-us-content {
    position: static;
    flex-direction: column;
    gap: 40px;
    align-items: center;
    width: 100%;
    height: auto;
  }

  .homepage__why-us-image {
    width: 100%;
    height: auto;
    max-width: 500px;
  }

  .homepage__why-us-features {
    gap: 24px;
    width: 100%;
    max-width: 600px;
  }

  .homepage__feature-item-description {
    margin-left: 56px;
    width: 100%;
    font-size: 18px;
  }

  .homepage__feature-item-title {
    font-size: 30px;
  }
}

@media (max-width: 640px) {
  .homepage__why-us-section {
    padding: 20px 16px 0;
    max-width: 100vw;
  }

  .homepage__why-us-title {
    top: 20px;
    font-size: 64px;
    height: 50px;
    left: 15px;
    width: 250px;
  }

  .homepage__why-us-content {
    gap: 32px;
  }

  .homepage__why-us-image {
    max-width: 100%;
  }

  .homepage__why-us-features {
    gap: 20px;
  }

  .homepage__feature-item-header {
    gap: 10px;
  }

  .homepage__feature-item-title {
    font-size: 24px;
    line-height: 28px;
  }

  .homepage__feature-item-description {
    margin-left: 44px;
    font-size: 16px;
    line-height: 20px;
  }
}

@media (max-width: 640px) {
  .homepage__content-engine-section {
    padding: 16px;
    max-width: 100%;
  }

  .homepage__content-engine-main-image {
    width: 100%;
    height: auto;
    max-height: 300px;
  }

  .homepage__content-engine-ratings {
    gap: 4px;
  }

  .homepage__content-engine-rating-star {
    width: 28px;
    height: 28px;
  }

  .homepage__content-engine-content {
    gap: 20px;
  }

  .homepage__content-engine-title {
    font-size: 36px;
  }

  .homepage__content-engine-title-text {
    font-size: 36px;
  }

  .homepage__content-engine-description {
    font-size: 20px;
  }

  .homepage__content-engine-description-text {
    font-size: 20px;
  }
}

/* Team Testimonial Section */
.homepage__team-testimonial-section {
  overflow: hidden;
  position: relative;
  margin: 0 auto;
  width: 100%;
  max-width: none;
  background-color: #f5f5f1;
  height: 871px;
}

.homepage__team-testimonial-background {
  position: absolute;
  top: 0;
  left: 0;
  height: 871px;
  width: 799px;
}

.homepage__team-testimonial-content {
  display: flex;
  position: absolute;
  flex-direction: column;
  gap: 40px;
  align-items: flex-start;
  height: 367px;
  left: 841px;
  top: 255px;
  width: 491px;
}

.homepage__team-testimonial-ratings {
  display: flex;
  position: relative;
  gap: 6px;
  align-items: center;
}

.homepage__team-testimonial-star {
  position: relative;
  aspect-ratio: 38/37;
  height: 37px;
  width: 38px;
}

.homepage__team-testimonial-text-content {
  display: flex;
  position: relative;
  flex-direction: column;
  gap: 28px;
  align-items: flex-start;
  align-self: stretch;
}

.homepage__team-testimonial-title {
  position: relative;
  font-family: "Baskerville Old Face", "Times New Roman", serif;
  font-size: 72px;
  letter-spacing: -0.025em;
  color: #966f33;
  line-height: 62.22px;
  width: 404px;
}

.homepage__team-testimonial-quote {
  position: relative;
  align-self: stretch;
  font-size: 20px;
  font-style: italic;
  line-height: 24px;
  color: black;
  height: 75px;
}

.homepage__team-testimonial-attribution {
  position: relative;
  height: 24px;
  width: 368px;
}

.homepage__team-testimonial-author {
  position: absolute;
  left: 0;
  top: 2px;
  height: 20px;
  font-size: 20px;
  letter-spacing: -0.025em;
  line-height: 20px;
  color: black;
  width: 296px;
}

.homepage__team-testimonial-divider {
  position: absolute;
  width: 36px;
  height: 0;
  background-color: black;
  border-top: 1px solid black;
  left: 313px;
  top: 13px;
}

.homepage__team-testimonial-arrow {
  position: absolute;
  left: 344px;
  top: 0;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

/* Responsive styles for Team Testimonial Section */
@media (max-width: 768px) {
  .homepage__team-testimonial-section {
    height: auto;
    min-height: 100vh;
    padding: 40px 20px;
    max-width: 991px;
  }

  .homepage__team-testimonial-content {
    position: relative;
    top: auto;
    left: auto;
    padding-top: 40px;
    margin: 0 auto;
    width: 100%;
    max-width: 600px;
  }

  .homepage__team-testimonial-title {
    width: 100%;
    font-size: 48px;
    letter-spacing: -0.025em;
  }

  .homepage__team-testimonial-quote {
    height: auto;
    font-size: 18px;
  }

  .homepage__team-testimonial-attribution {
    width: 100%;
    height: auto;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .homepage__team-testimonial-author {
    position: relative;
    top: auto;
    left: auto;
    width: auto;
    font-size: 18px;
    letter-spacing: -0.025em;
  }

  .homepage__team-testimonial-divider {
    position: relative;
    top: auto;
    left: auto;
    margin: 10px 0;
    width: 100%;
  }

  .homepage__team-testimonial-arrow {
    position: relative;
    top: auto;
    left: auto;
    margin: 0;
  }
}

@media (max-width: 640px) {
  .homepage__team-testimonial-section {
    padding: 20px 16px;
  }

  .homepage__team-testimonial-content {
    gap: 32px;
    padding-top: 20px;
  }

  .homepage__team-testimonial-ratings {
    gap: 4px;
  }

  .homepage__team-testimonial-star {
    width: 32px;
    height: 31px;
  }

  .homepage__team-testimonial-title {
    font-size: 36px;
    letter-spacing: -0.025em;
  }

  .homepage__team-testimonial-quote {
    font-size: 16px;
    line-height: 20px;
  }
}

/* Call to Action Section */
.homepage__cta-section {
  overflow: hidden;
  position: relative;
  margin: 0 auto;
  width: 100%;
  max-width: none;
  background-color: #fafaf5;
  height: 871px;
}

.homepage__cta-container {
  display: flex;
  position: absolute;
  flex-direction: column;
  gap: 56px;
  align-items: flex-start;
  height: 485px;
  left: 33px;
  top: 101px;
  width: 758px;
}

.homepage__cta-content {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
}

.homepage__cta-title {
  position: relative;
  align-self: stretch;
  font-size: 96px;
  color: #966f33;
  height: 327px;
  line-height: 99.9px;
  font-family: "Baskerville Old Face", "Times New Roman", serif;
}

.homepage__cta-description {
  position: relative;
  font-size: 20px;
  font-weight: 300;
  letter-spacing: -0.025em;
  line-height: 20px;
  color: #71717a;
  font-family: "Source Sans Pro", sans-serif;
}

.homepage__cta-button {
  display: flex;
  position: relative;
  gap: 10px;
  justify-content: center;
  align-items: center;
  padding: 20px 40px;
  background-color: #966f33;
  cursor: pointer;
  height: 65px;
  width: 216px;
}

.homepage__cta-button-text {
  position: relative;
  font-size: 20px;
  letter-spacing: 0.05em;
  line-height: 24px;
  color: white;
  font-family: "Source Sans Pro", sans-serif;
}

.homepage__cta-image {
  position: absolute;
  top: 0;
  flex-shrink: 0;
  aspect-ratio: 1011/1190;
  height: 1190px;
  left: 1178px;
  width: 1011px;
}

/* Responsive styles for Call to Action Section */
@media (max-width: 768px) {
  .homepage__cta-section {
    height: auto;
    min-height: 100vh;
    max-width: 991px;
  }

  .homepage__cta-container {
    gap: 40px;
    height: auto;
    left: 5%;
    top: 50px;
    width: 90%;
  }

  .homepage__cta-title {
    height: auto;
    font-size: 72px;
    line-height: 66px;
  }

  .homepage__cta-description {
    font-size: 18px;
    line-height: 20px;
  }

  .homepage__cta-button {
    padding: 16px 32px;
    height: 55px;
    width: 180px;
  }

  .homepage__cta-button-text {
    font-size: 18px;
  }

  .homepage__cta-image {
    left: 50%;
    transform: translateX(-50%);
    height: 706px;
    top: 400px;
    width: 600px;
  }
}

@media (max-width: 640px) {
  .homepage__cta-section {
    padding-bottom: 48px;
    height: auto;
    min-height: 100vh;
    max-width: 100%;
  }

  .homepage__cta-container {
    gap: 32px;
    height: auto;
    left: 5%;
    top: 30px;
    width: 90%;
  }

  .homepage__cta-title {
    height: auto;
    font-size: 48px;
    line-height: 40px;
  }

  .homepage__cta-description {
    margin-top: 20px;
    font-size: 16px;
    line-height: 16px;
  }

  .homepage__cta-button {
    padding: 12px 24px;
    width: 160px;
    height: 50px;
  }

  .homepage__cta-button-text {
    font-size: 16px;
  }

  .homepage__cta-image {
    left: 50%;
    transform: translateX(-50%);
    height: 412px;
    top: 350px;
    width: 350px;
  }
}

/* Testimonials Section */
.homepage__testimonials-section {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 96px 80px;
  background-color: #fbfbf9;
  width: 100%;
}

.homepage__testimonials-container {
  display: flex;
  flex-direction: column;
  max-width: 100%;
  width: 790px;
}

.homepage__testimonials-content {
  width: 100%;
}

.homepage__testimonials-header {
  max-width: 100%;
  line-height: 1;
  color: #966f33;
  width: 570px;
}

.homepage__testimonials-title {
  font-size: 48px;
  letter-spacing: 2px;
  font-family: "Baskerville Old Face", "Times New Roman", serif;
  color: #966f33;
}

.homepage__testimonials-quote-mark {
  margin-top: 6px;
  font-size: 160px;
  letter-spacing: 6.4px;
  font-family: "Baskerville Old Face", "Times New Roman", serif;
  color: #966f33;
}

.homepage__testimonials-quote-box {
  display: flex;
  gap: 10px;
  justify-content: center;
  align-items: center;
  padding: 48px 64px 48px 0;
  width: 100%;
  font-size: 48px;
  background-color: #fffbeb;
  line-height: 56px;
  min-height: 435px;
  color: #525252;
  letter-spacing: 2.08px;
}

.homepage__testimonials-quote-text {
  align-self: stretch;
  margin: auto 0;
  width: 652px;
  font-family: "Barlow Condensed", sans-serif;
}

.homepage__testimonials-navigation {
  display: flex;
  gap: 6px;
  align-items: center;
  align-self: end;
  margin-top: 32px;
}

.homepage__testimonials-nav-dot {
  object-fit: contain;
  flex-shrink: 0;
  align-self: stretch;
  margin: auto 0;
  aspect-ratio: 1.03;
  width: 38px;
}

/* Responsive styles for Testimonials Section */
@media (max-width: 768px) {
  .homepage__testimonials-section {
    padding: 96px 20px;
  }

  .homepage__testimonials-title {
    font-size: 32px;
  }

  .homepage__testimonials-quote-mark {
    font-size: 32px;
  }

  .homepage__testimonials-quote-box {
    padding: 20px;
    font-size: 32px;
    line-height: 40px;
  }

  .homepage__testimonials-quote-text {
    width: 100%;
  }
}
