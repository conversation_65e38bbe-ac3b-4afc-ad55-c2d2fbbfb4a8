import { Link } from '@remix-run/react';
import Button from './Button';
import './Navbar.css';

interface NavbarProps {
  onJoinWaitlist?: () => void;
}

const Navigation = () => {
  return (
    <div className="navbar__nav">
      <Link to="/" className="navbar__nav-link">Home</Link>
      <span className="navbar__nav-separator"> • </span>
      <Link to="/who-we-are" className="navbar__nav-link">Who we are</Link>
      <span className="navbar__nav-separator"> • </span>
      <Link to="/contact-us" className="navbar__nav-link">Contact Us</Link>
    </div>
  );
};

export default function Navbar({ onJoinWaitlist }: NavbarProps) {

  return (
    <nav className="navbar">
      <div className="navbar__brand">
        LawVriksh
      </div>
      
      <div className="navbar__content">
        <Navigation />
        
        <Button onClick={onJoinWaitlist}>
          Join Waitlist
        </Button>
      </div>
    </nav>
  );
}
