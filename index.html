<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LawVriksh - Digital Personal Data Protection Act</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Baskerville+Old+Face&family=Source+Sans+Pro:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --background: hsl(30, 15%, 96%);
            --foreground: hsl(30, 10%, 15%);
            --card: hsl(0, 0%, 100%);
            --card-foreground: hsl(30, 10%, 15%);
            --primary: hsl(28, 84%, 44%);
            --primary-foreground: hsl(0, 0%, 100%);
            --secondary: hsl(35, 25%, 88%);
            --secondary-foreground: hsl(30, 10%, 25%);
            --muted: hsl(30, 15%, 92%);
            --muted-foreground: hsl(30, 8%, 45%);
            --accent: hsl(35, 80%, 55%);
            --accent-foreground: hsl(0, 0%, 100%);
            --border: hsl(30, 15%, 88%);
            --input: hsl(30, 15%, 88%);
            --legal-gold: hsl(42, 88%, 55%);
            --legal-bronze: hsl(28, 65%, 35%);
            --legal-cream: hsl(45, 35%, 95%);
            --legal-text: hsl(30, 10%, 20%);
            --text-dark: hsl(30, 10%, 20%);
            --toolbar-bg: hsl(30, 15%, 94%);
            --bg-toolbar: hsl(30, 15%, 94%);
            --border-color: hsl(30, 15%, 88%);
            --sidebar-bg: hsl(0, 0%, 98%);
        }

        body {
            font-family: 'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--background);
            color: var(--foreground);
            line-height: 1.6;
        }

        .container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            padding-top: 64px; /* Account for fixed header */
        }

        /* Header */
        .header {
            height: 64px;
            background-color: var(--card);
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
            padding: 0 24px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .brand-name {
            font-family: 'Baskerville Old Face', serif;
            font-weight: 600;
            font-size: 1.5rem;
            color: var(--legal-text);
        }

        .header-center {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-left: 24px;
        }

        .header-right {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 20px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-edit {
            background: var(--legal-bronze);
            color: white;
        }

        .btn-edit:hover {
            background: rgba(var(--legal-bronze), 0.9);
        }

        .btn-research {
            background: transparent;
            color: var(--legal-text);
            border: 1px solid var(--border);
        }

        .btn-research:hover {
            background: var(--muted);
        }

        .saved-indicator {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #22c55e;
            font-size: 14px;
            font-weight: 500;
        }

        .user-profile {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--muted);
            border: 2px solid var(--border);
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .user-profile:hover {
            border-color: var(--legal-gold);
        }

        .user-profile svg {
            width: 16px;
            height: 16px;
            color: var(--legal-text);
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            width: 8px;
            height: 8px;
            background: #ef4444;
            border-radius: 50%;
            border: 1px solid white;
        }

        .btn-publish {
            background: var(--legal-bronze);
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            border: none;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-publish:hover {
            background: rgba(var(--legal-bronze), 0.9);
        }

        /* Main Layout */
        .main-layout {
            display: flex;
            flex: 1;
            position: relative;
        }

        /* Sidebar toggle button */
        .sidebar-toggle {
            background: var(--legal-bronze);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 66px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            background: rgba(var(--legal-bronze), 0.9);
        }

        /* Left Toolbar */
        .toolbar {
            position: fixed;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 20;
            
        }

        .toolbar-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            border-radius: 9999px;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            background-color: var(--toolbar-bg);
            border: 1px solid var(--border);
        }

        .toolbar-icon {
            padding: 12px;
            border-radius: 9999px;
            border: 1px solid transparent;
            height: 49.6px;
            transition: all 0.3s ease;
            cursor: pointer;
            margin: 4px 0;
        }

        .toolbar-icon:hover {
            border-color: var(--border);
        }

        .toolbar-icon.active {
            border-color: var(--border);
            background-color: white;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        .toolbar-divider {
            width: 66.666667%;
            border-top: 1px solid var(--border);
            margin: 8px auto;
        }

        .toolbar-icon svg {
            height: 24px;
            width: 24px;
            color: var(--text-dark);
        }

        /* Main Content */
        .main-content {
            flex: 1;
            overflow-y: auto;
            padding: 32px;
            margin-left: 50px;
        }

        .content-wrapper {
            max-width: 1024px;
            margin: 0 auto;
        }

        .hero-image {
            margin-bottom: 32px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 20px -4px rgba(var(--legal-bronze), 0.15);
        }

        .hero-image img {
            width: 100%;
            height: 256px;
            object-fit: cover;
        }

        .article {
            color: var(--legal-text);
        }

        .article h1 {
            font-family: 'Baskerville Old Face', serif;
            font-size: 2.25rem;
            font-weight: bold;
            margin-bottom: 24px;
            line-height: 1.2;
        }

        .article h2 {
            font-family: 'Baskerville Old Face', serif;
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--legal-bronze);
            margin: 32px 0 16px 0;
        }

        .article h3 {
            font-family: 'Baskerville Old Face', serif;
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--legal-bronze);
            margin: 24px 0 12px 0;
        }

        .article p {
            margin-bottom: 24px;
            line-height: 1.7;
        }

        .article ul {
            margin: 16px 0;
            padding-left: 24px;
        }

        .article li {
            margin-bottom: 8px;
        }

        /* Right Sidebar */
        .right-sidebar {
            position: fixed;
            top: 64px;
            right: 0;
            width: 320px;
            height: calc(100vh - 64px);
            background-color: var(--sidebar-bg);
            border-left: 1px solid var(--border);
            transform: translateX(0);
            transition: transform 0.3s ease;
            z-index: 500;
            overflow-y: auto;
        }

        .right-sidebar.collapsed {
            transform: translateX(100%);
        }

        .sidebar-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 24px;
            border-bottom: 1px solid var(--border);
        }

        .sidebar-title {
            font-family: 'Baskerville Old Face', serif;
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--legal-text);
        }

        .close-btn {
            width: 32px;
            height: 32px;
            border: none;
            background: transparent;
            cursor: pointer;
            border-radius: 4px;
            transition: background 0.3s ease;
        }

        .close-btn:hover {
            background: var(--muted);
        }

        .sidebar-content {
            padding: 24px;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: var(--legal-text);
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border);
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--legal-gold);
            box-shadow: 0 0 0 3px rgba(var(--legal-gold), 0.1);
        }

        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }

        .toggle-group {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .toggle-info {
            flex: 1;
        }

        .toggle-description {
            font-size: 12px;
            color: var(--muted-foreground);
            margin-top: 4px;
        }

        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: var(--legal-gold);
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .toggle-switch::before {
            content: '';
            position: absolute;
            top: 2px;
            right: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }

        .toggle-switch.off {
            background: var(--muted);
        }

        .toggle-switch.off::before {
            transform: translateX(-20px);
        }

        .upload-area {
            border: 2px dashed var(--border);
            border-radius: 8px;
            padding: 32px;
            text-align: center;
            background: rgba(var(--muted), 0.3);
        }

        .upload-icon {
            font-size: 32px;
            color: var(--muted-foreground);
            margin-bottom: 8px;
        }

        .upload-text {
            font-size: 14px;
            color: var(--muted-foreground);
        }

        .publish-btn {
            width: 100%;
            padding: 12px;
            background: var(--legal-bronze);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .publish-btn:hover {
            background: rgba(var(--legal-bronze), 0.9);
        }

        /* Content adjustment when sidebar is open */
        .main-content {
            margin-right: 320px;
            transition: margin-right 0.3s ease;
        }

        .main-content.sidebar-collapsed {
            margin-right: 0;
        }

        @media (min-width: 768px) {
            .toolbar {
                left: 24px;
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .right-sidebar {
                width: 100%;
            }
            
            .main-content {
                margin-right: 0;
            }
            
            .main-content.sidebar-collapsed {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-left">
                <div class="logo-section">
                    <span class="brand-name">LawVriksh</span>
                </div>
                
                <div class="header-center">
                    <button class="btn btn-edit">✏️ Edit</button>
                    <button class="btn btn-research">Research</button>
                </div>
            </div>
            
            <div class="header-right">
                <div class="saved-indicator">
                    <span>✓</span>
                    <span>Saved</span>
                </div>
                <div class="user-profile">
                    <svg fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0" />
                    </svg>
                    <div class="notification-badge"></div>
                </div>
                <div class="user-profile">
                    <svg fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                    </svg>
                </div>
                <button class="sidebar-toggle" onclick="toggleSidebar()">Settings</button>
                <button class="btn-publish">Publish</button>
            </div>
        </header>

        <!-- Left Toolbar -->
        <aside class="toolbar">
            <div class="toolbar-container">
                <a href="#" class="toolbar-icon">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9.53 16.122a3 3 0 00-5.78 1.128 2.25 2.25 0 01-2.4 2.245 4.5 4.5 0 008.4-2.245c0-.399-.078-.78-.22-1.128zm0 0a15.998 15.998 0 003.388-1.62m-5.043-.025a15.998 15.998 0 011.622-3.385m5.043.025a15.998 15.998 0 001.622-3.385m3.388 1.62a15.998 15.998 0 00-1.62-3.385m-5.043-.025a15.998 15.998 0 01-3.388-1.621m-5.043.025a15.998 15.998 0 00-3.388 1.622m5.043.025a15.998 15.998 0 01-1.622 3.385m3.388-1.622a15.998 15.998 0 01-1.622 3.385M6 12a6 6 0 1112 0 6 6 0 01-12 0z" />
                    </svg>
                </a>
                <a href="#" class="toolbar-icon">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M7.5 8.25h9m-9 3H12m-9.75 3h9.75m-9.75 2.25h9.75m4.5-4.5v6.75a2.25 2.25 0 01-2.25 2.25H5.25a2.25 2.25 0 01-2.25-2.25V5.25a2.25 2.25 0 012.25-2.25h4.5m4.5 0v6.75m0 0l-3.75-3.75M17.25 9l-3.75 3.75" />
                    </svg>
                </a>
                <a href="#" class="toolbar-icon">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 6h9.75M10.5 6a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-9.75 0h9.75" />
                    </svg>
                </a>
                <a href="#" class="toolbar-icon active">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.898 20.572L16.5 21.75l-.398-1.178a3.375 3.375 0 00-2.455-2.456L12.75 18l1.178-.398a3.375 3.375 0 002.455-2.456L16.5 14.25l.398 1.178a3.375 3.375 0 002.456 2.456l1.178.398-1.178.398a3.375 3.375 0 00-2.456 2.456z" />
                    </svg>
                </a>
                <div class="toolbar-divider"></div>
                <a href="#" class="toolbar-icon" style="border-color: var(--border);">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                    </svg>
                </a>
            </div>
        </aside>

        <!-- Main Layout -->
        <div class="main-layout">

            <!-- Main Content -->
            <main class="main-content" id="mainContent">
                <div class="content-wrapper">
                    <!-- Hero Image -->
                    <div class="hero-image">
                        <img src="https://images.unsplash.com/photo-1589829545856-d10d557cf95f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                             alt="Legal books and gavel representing law and justice" />
                    </div>

                    <!-- Article Content -->
                    <article class="article">
                        <h1>Data Privacy Isn't Optional Anymore – Understanding India's Digital Personal Data Protection Act</h1>

                        <p>In today's digital economy, data is the new oil—but if misused, it can become toxic. With increasing instances of data breaches, surveillance concerns, and privacy violations, the Indian government has taken a landmark step by enacting the Digital Personal Data Protection Act, 2023 (DPDPA).</p>
                        
                        <p>This law aims to strike a balance between the individual's right to privacy and the need for organizations to use data for legitimate purposes.</p>
                        
                        <p>Whether you're a tech startup, an HR head, or a digital service provider, this law applies to you—and the time to act is now.</p>

                        <h2>What Is the Digital Personal Data Protection Act, 2023?</h2>
                        
                        <p>The DPDPA is India's first comprehensive law dedicated solely to protecting personal data in the digital realm. It lays down the rights of individuals (called Data Principals) and the responsibilities of entities collecting data (called Data Fiduciaries).</p>
                        
                        <p>The law is designed to be technology-neutral, industry-agnostic, and applicable to both government and private entities.</p>

                        <h3>Key Features of the DPDPA</h3>
                        
                        <ul>
                            <li>Consent-based data processing</li>
                            <li>Data localization requirements</li>
                            <li>Rights of data principals</li>
                            <li>Penalties for non-compliance</li>
                            <li>Cross-border data transfer regulations</li>
                        </ul>
                    </article>
                </div>
            </main>

            <!-- Right Sidebar -->
            <div class="right-sidebar" id="rightSidebar">
                <div class="sidebar-header">
                    <h2 class="sidebar-title">Post Settings</h2>
                    <button class="close-btn" onclick="toggleSidebar()">✕</button>
                </div>

                <div class="sidebar-content">
                    <div class="form-group">
                        <label class="form-label" for="seo-title">SEO Title</label>
                        <input type="text" id="seo-title" class="form-input" />
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="seo-description">SEO Description</label>
                        <textarea id="seo-description" class="form-input form-textarea"></textarea>
                    </div>

                    <div class="form-group">
                        <div class="toggle-group">
                            <div class="toggle-info">
                                <label class="form-label">Table of Content</label>
                                <div class="toggle-description">Do you want to add table of Content?</div>
                            </div>
                            <div class="toggle-switch" id="tocToggle" onclick="toggleSwitch('tocToggle')"></div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="article-slug">Article Slug</label>
                        <input type="text" id="article-slug" class="form-input" />
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="select-tags">Select Tags</label>
                        <input type="text" id="select-tags" class="form-input" />
                    </div>

                    <div class="form-group">
                        <label class="form-label">Custom Image</label>
                        <div class="upload-area">
                            <div class="upload-icon">📁</div>
                            <div class="upload-text">Drop n Drop</div>
                        </div>
                    </div>

                    <button class="publish-btn">Publish</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('rightSidebar');
            const mainContent = document.getElementById('mainContent');
            
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('sidebar-collapsed');
        }

        function toggleSwitch(id) {
            const toggle = document.getElementById(id);
            toggle.classList.toggle('off');
        }

        // Initialize toggle switch as on
        document.addEventListener('DOMContentLoaded', function() {
            // Table of content toggle is on by default (no 'off' class)
        });

        // Add smooth hover effects for buttons
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('.btn, .tool-btn, .close-btn');
            buttons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05)';
                });
                button.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        });
    </script>
</body>
</html>